// Configuración global
let extractedData = {};
let currentFileIndex = 0;
let totalFiles = 0;
let isAppInitialized = false;

// Inicialización de la aplicación (llamada desde index.html después de cargar PDF.js)
function initializeApp() {
    if (isAppInitialized) return;

    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('uploadArea');

    if (!fileInput || !uploadArea) {
        console.error('❌ Elementos de la interfaz no encontrados');
        return;
    }

    // Event listeners para el input de archivos
    fileInput.addEventListener('change', handleFileSelect);

    // Event listeners para drag and drop
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);

    isAppInitialized = true;
    console.log('🎯 Extractor PDF inicializado correctamente');
}

// Auto-inicializar si PDF.js ya está disponible
if (typeof pdfjsLib !== 'undefined') {
    document.addEventListener('DOMContentLoaded', initializeApp);
}



// Manejo de selección de archivos
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    if (files.length > 0) {
        processFiles(files);
    }
}

// Manejo de drag over
function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    document.getElementById('uploadArea').classList.add('dragover');
}

// Manejo de drag leave
function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    document.getElementById('uploadArea').classList.remove('dragover');
}

// Manejo de drop
function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    document.getElementById('uploadArea').classList.remove('dragover');
    
    const files = Array.from(event.dataTransfer.files).filter(file => file.type === 'application/pdf');
    if (files.length > 0) {
        processFiles(files);
    } else {
        showError('Por favor, selecciona solo archivos PDF válidos.');
    }
}

// Procesar archivos PDF
async function processFiles(files) {
    if (typeof pdfjsLib === 'undefined') {
        showError('Error: PDF.js no está cargado. Por favor, asegúrate de que el archivo libs/pdf.min.js esté presente.');
        return;
    }
    
    totalFiles = files.length;
    currentFileIndex = 0;
    extractedData = {};
    
    // Mostrar sección de estado
    showStatusSection();
    hideResultsSection();
    
    try {
        for (let i = 0; i < files.length; i++) {
            currentFileIndex = i;
            updateProgress();
            updateStatusText(`Procesando ${files[i].name}...`);
            
            const text = await extractTextFromPDF(files[i]);
            extractedData[files[i].name] = {
                text: text,
                fileName: files[i].name,
                fileSize: formatFileSize(files[i].size),
                extractedAt: new Date().toLocaleString('es-ES')
            };
        }
        
        // Completar procesamiento
        currentFileIndex = totalFiles;
        updateProgress();
        updateStatusText('¡Extracción completada!');
        
        setTimeout(() => {
            hideStatusSection();
            showResults();
        }, 1000);
        
    } catch (error) {
        console.error('Error procesando archivos:', error);
        hideStatusSection();
        showError(`Error procesando archivos: ${error.message}`);
    }
}

// Extraer texto de un archivo PDF
async function extractTextFromPDF(file) {
    return new Promise((resolve, reject) => {
        const fileReader = new FileReader();
        
        fileReader.onload = async function(event) {
            try {
                const typedArray = new Uint8Array(event.target.result);
                const pdf = await pdfjsLib.getDocument(typedArray).promise;
                let fullText = '';
                
                // Extraer texto de todas las páginas
                for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                    const page = await pdf.getPage(pageNum);
                    const textContent = await page.getTextContent();
                    
                    const pageText = textContent.items
                        .map(item => item.str)
                        .join(' ');
                    
                    fullText += `\n--- PÁGINA ${pageNum} ---\n${pageText}\n`;
                }
                
                resolve(fullText.trim());
            } catch (error) {
                reject(new Error(`Error leyendo PDF: ${error.message}`));
            }
        };
        
        fileReader.onerror = function() {
            reject(new Error('Error leyendo el archivo'));
        };
        
        fileReader.readAsArrayBuffer(file);
    });
}

// Mostrar resultados
function showResults() {
    const resultsSection = document.getElementById('resultsSection');
    const filesTabs = document.getElementById('filesTabs');
    const textOutput = document.getElementById('textOutput');
    
    // Limpiar tabs existentes
    filesTabs.innerHTML = '';
    
    // Crear tabs para cada archivo
    const fileNames = Object.keys(extractedData);
    fileNames.forEach((fileName, index) => {
        const tab = document.createElement('button');
        tab.className = `tab ${index === 0 ? 'active' : ''}`;
        tab.textContent = fileName;
        tab.onclick = () => showFileContent(fileName);
        filesTabs.appendChild(tab);
    });
    
    // Mostrar contenido del primer archivo
    if (fileNames.length > 0) {
        showFileContent(fileNames[0]);
    }
    
    resultsSection.style.display = 'block';
}

// Mostrar contenido de un archivo específico
function showFileContent(fileName) {
    const textOutput = document.getElementById('textOutput');
    const data = extractedData[fileName];
    
    // Actualizar tabs activos
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
        if (tab.textContent === fileName) {
            tab.classList.add('active');
        }
    });
    
    // Mostrar información del archivo y texto extraído
    const fileInfo = `📄 Archivo: ${data.fileName}
📊 Tamaño: ${data.fileSize}
🕒 Extraído: ${data.extractedAt}
📝 Caracteres: ${data.text.length.toLocaleString()}

${'='.repeat(80)}

${data.text}`;
    
    textOutput.textContent = fileInfo;
}

// Funciones de utilidad para la interfaz
function showStatusSection() {
    document.getElementById('statusSection').style.display = 'block';
}

function hideStatusSection() {
    document.getElementById('statusSection').style.display = 'none';
}

function hideResultsSection() {
    document.getElementById('resultsSection').style.display = 'none';
}

function updateProgress() {
    const progress = (currentFileIndex / totalFiles) * 100;
    document.getElementById('progressFill').style.width = `${progress}%`;
}

function updateStatusText(text) {
    document.getElementById('statusText').textContent = text;
}

// Limpiar resultados
function clearResults() {
    extractedData = {};
    hideResultsSection();
    document.getElementById('fileInput').value = '';
}

// Descargar resultados como archivo de texto
function downloadResults() {
    if (Object.keys(extractedData).length === 0) {
        showError('No hay datos para descargar.');
        return;
    }
    
    let content = `EXTRACTOR WEB DE COTIZACIONES PDF - RESULTADOS\n`;
    content += `Generado: ${new Date().toLocaleString('es-ES')}\n`;
    content += `${'='.repeat(80)}\n\n`;
    
    Object.values(extractedData).forEach((data, index) => {
        content += `ARCHIVO ${index + 1}: ${data.fileName}\n`;
        content += `Tamaño: ${data.fileSize}\n`;
        content += `Extraído: ${data.extractedAt}\n`;
        content += `${'='.repeat(50)}\n`;
        content += `${data.text}\n\n`;
        content += `${'='.repeat(80)}\n\n`;
    });
    
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `extraccion_pdf_${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Mostrar errores
function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorModal').style.display = 'flex';
}

// Cerrar modal
function closeModal() {
    document.getElementById('errorModal').style.display = 'none';
}

// Formatear tamaño de archivo
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Manejo de errores globales
window.addEventListener('error', function(event) {
    console.error('Error global:', event.error);
    showError(`Error inesperado: ${event.error.message}`);
});

console.log('🚀 main.js cargado correctamente');
