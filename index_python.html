<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extractor PDF con Python - Versión Autocontenida</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 12px;
            padding: 60px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: rgba(52, 152, 219, 0.05);
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: rgba(52, 152, 219, 0.1);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .upload-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .upload-area h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .upload-area p {
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .upload-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .status-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: none;
        }

        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #ecf0f1;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }

        .results-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .results-header h2 {
            color: #2c3e50;
            font-size: 1.8rem;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .files-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .tab {
            padding: 10px 20px;
            background: #ecf0f1;
            border: none;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab.active {
            background: #3498db;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #bdc3c7;
        }

        .text-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .python-status {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .python-status.ready {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
        }

        .python-status.error {
            background: rgba(220, 53, 69, 0.1);
            border-color: #dc3545;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .upload-area {
                padding: 40px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🐍 Extractor PDF con Python</h1>
            <p class="subtitle">Versión Autocontenida con Extracción Real de Texto</p>
        </header>

        <!-- Python Status -->
        <div class="python-status" id="pythonStatus">
            <div>🔄 Cargando Python en el navegador...</div>
            <div><small>Esto puede tomar unos segundos la primera vez</small></div>
        </div>

        <!-- Upload Section -->
        <section class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📄</div>
                <h3>Selecciona archivos PDF</h3>
                <p>Arrastra y suelta archivos PDF aquí o haz clic para seleccionar</p>
                <input type="file" id="fileInput" accept=".pdf" multiple style="display: none;">
                <button class="upload-btn" id="selectBtn" disabled>
                    Cargando Python...
                </button>
            </div>
        </section>

        <!-- Status Section -->
        <section class="status-section" id="statusSection">
            <div class="status-info">
                <div class="spinner"></div>
                <span id="statusText">Procesando archivos...</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </section>

        <!-- Results Section -->
        <section class="results-section" id="resultsSection">
            <div class="results-header">
                <h2>📋 Resultados de Extracción</h2>
                <div>
                    <button class="btn btn-secondary" onclick="clearResults()">Limpiar</button>
                    <button class="btn btn-primary" onclick="downloadResults()">Descargar Texto</button>
                </div>
            </div>
            
            <div class="files-tabs" id="filesTabs">
                <!-- Tabs will be generated dynamically -->
            </div>
            
            <div class="text-output" id="textOutput">
                <!-- Extracted text will be displayed here -->
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <p>🐍 Powered by Pyodide (Python en el navegador)</p>
            <p>🛡️ 100% local - Sin instalación - Extracción real de texto</p>
        </footer>
    </div>

    <!-- Pyodide (Python en el navegador) -->
    <script src="https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js"></script>
    
    <script>
        // Variables globales
        let pyodide;
        let extractedData = {};
        let currentFileIndex = 0;
        let totalFiles = 0;
        let pythonReady = false;

        // Inicializar Pyodide
        async function initializePython() {
            try {
                console.log('🐍 Cargando Pyodide...');
                pyodide = await loadPyodide();
                
                console.log('📦 Instalando paquetes Python...');
                await pyodide.loadPackage(['micropip']);
                
                // Instalar PyPDF2 para extracción de texto
                await pyodide.runPython(`
                    import micropip
                    await micropip.install('PyPDF2')
                `);
                
                console.log('✅ Python listo para usar');
                pythonReady = true;
                updatePythonStatus('ready', '✅ Python listo - Puedes cargar PDFs');
                enableUpload();
                
            } catch (error) {
                console.error('❌ Error cargando Python:', error);
                updatePythonStatus('error', '❌ Error cargando Python - Usa versión básica');
                enableUploadBasic();
            }
        }

        // Actualizar estado de Python
        function updatePythonStatus(status, message) {
            const statusElement = document.getElementById('pythonStatus');
            statusElement.className = `python-status ${status}`;
            statusElement.innerHTML = `<div>${message}</div>`;
        }

        // Habilitar carga con Python
        function enableUpload() {
            const selectBtn = document.getElementById('selectBtn');
            selectBtn.disabled = false;
            selectBtn.textContent = 'Seleccionar PDFs';
            initializeApp();
        }

        // Habilitar carga básica (fallback)
        function enableUploadBasic() {
            const selectBtn = document.getElementById('selectBtn');
            selectBtn.disabled = false;
            selectBtn.textContent = 'Seleccionar PDFs (Modo Básico)';
            initializeApp();
        }

        // Inicializar aplicación
        function initializeApp() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const selectBtn = document.getElementById('selectBtn');

            // Event listeners
            fileInput.addEventListener('change', handleFileSelect);
            
            selectBtn.addEventListener('click', function(event) {
                event.preventDefault();
                fileInput.click();
            });

            uploadArea.addEventListener('click', function(event) {
                if (event.target !== selectBtn && !selectBtn.contains(event.target)) {
                    fileInput.click();
                }
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', function(event) {
                event.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(event) {
                event.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(event) {
                event.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = event.dataTransfer.files;
                const pdfFiles = Array.from(files).filter(file => 
                    file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
                );
                
                if (pdfFiles.length > 0) {
                    processFiles(pdfFiles);
                } else {
                    alert('Por favor, selecciona solo archivos PDF válidos.');
                }
            });

            console.log('🎯 Aplicación inicializada');
        }

        // Manejar selección de archivos
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            if (files.length > 0) {
                processFiles(files);
            }
        }

        // Procesar archivos
        async function processFiles(files) {
            totalFiles = files.length;
            currentFileIndex = 0;
            extractedData = {};
            
            // Mostrar sección de estado
            document.getElementById('statusSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            
            try {
                for (let i = 0; i < files.length; i++) {
                    currentFileIndex = i;
                    updateProgress();
                    updateStatusText(`Procesando ${files[i].name}...`);
                    
                    let text;
                    if (pythonReady) {
                        text = await extractTextWithPython(files[i]);
                    } else {
                        text = await extractTextBasic(files[i]);
                    }
                    
                    extractedData[files[i].name] = {
                        text: text,
                        fileName: files[i].name,
                        fileSize: formatFileSize(files[i].size),
                        extractedAt: new Date().toLocaleString('es-ES'),
                        method: pythonReady ? 'Python (PyPDF2)' : 'Básico'
                    };
                }
                
                currentFileIndex = totalFiles;
                updateProgress();
                updateStatusText('¡Extracción completada!');
                
                setTimeout(() => {
                    document.getElementById('statusSection').style.display = 'none';
                    showResults();
                }, 1000);
                
            } catch (error) {
                console.error('Error procesando archivos:', error);
                document.getElementById('statusSection').style.display = 'none';
                alert(`Error procesando archivos: ${error.message}`);
            }
        }

        // Extraer texto con Python
        async function extractTextWithPython(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = async function(event) {
                    try {
                        const arrayBuffer = event.target.result;
                        const uint8Array = new Uint8Array(arrayBuffer);
                        
                        // Pasar datos a Python
                        pyodide.globals.set('pdf_data', uint8Array);
                        
                        const pythonCode = `
import PyPDF2
import io

# Crear objeto de archivo desde los datos
pdf_file = io.BytesIO(pdf_data.tobytes())

# Leer PDF
pdf_reader = PyPDF2.PdfReader(pdf_file)

# Extraer texto de todas las páginas
full_text = ""
for page_num in range(len(pdf_reader.pages)):
    page = pdf_reader.pages[page_num]
    page_text = page.extract_text()
    full_text += f"\\n--- PÁGINA {page_num + 1} ---\\n{page_text}\\n"

full_text
                        `;
                        
                        const result = await pyodide.runPythonAsync(pythonCode);
                        resolve(result || 'No se pudo extraer texto del PDF');
                        
                    } catch (error) {
                        console.error('Error en Python:', error);
                        // Fallback a método básico
                        const basicText = await extractTextBasic(file);
                        resolve(basicText);
                    }
                };
                reader.onerror = () => reject(new Error('Error leyendo archivo'));
                reader.readAsArrayBuffer(file);
            });
        }

        // Extraer texto básico (fallback)
        async function extractTextBasic(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const arrayBuffer = e.target.result;
                    const uint8Array = new Uint8Array(arrayBuffer);
                    
                    // Verificar si es un PDF válido
                    const pdfHeader = String.fromCharCode.apply(null, uint8Array.slice(0, 5));
                    
                    if (pdfHeader === '%PDF-') {
                        const info = `📄 ARCHIVO PDF DETECTADO (MODO BÁSICO)

🔍 INFORMACIÓN DEL ARCHIVO:
- Nombre: ${file.name}
- Tamaño: ${formatFileSize(file.size)}
- Tipo: ${file.type}
- Última modificación: ${new Date(file.lastModified).toLocaleString('es-ES')}

⚠️ EXTRACCIÓN LIMITADA:
Este archivo fue procesado en modo básico porque Python no está disponible.

🚀 PARA EXTRACCIÓN COMPLETA:
Recarga la página y espera a que Python se cargue completamente.

📋 ESTADO:
- Archivo PDF válido detectado
- Versión PDF: ${pdfHeader}
- Procesamiento básico completado`;
                        
                        resolve(info);
                    } else {
                        resolve('❌ Error: El archivo no parece ser un PDF válido.');
                    }
                };
                reader.readAsArrayBuffer(file);
            });
        }

        // Mostrar resultados
        function showResults() {
            const resultsSection = document.getElementById('resultsSection');
            const filesTabs = document.getElementById('filesTabs');
            
            // Limpiar tabs existentes
            filesTabs.innerHTML = '';
            
            // Crear tabs para cada archivo
            const fileNames = Object.keys(extractedData);
            fileNames.forEach((fileName, index) => {
                const tab = document.createElement('button');
                tab.className = `tab ${index === 0 ? 'active' : ''}`;
                tab.textContent = fileName;
                tab.onclick = () => showFileContent(fileName);
                filesTabs.appendChild(tab);
            });
            
            // Mostrar contenido del primer archivo
            if (fileNames.length > 0) {
                showFileContent(fileNames[0]);
            }
            
            resultsSection.style.display = 'block';
        }

        // Mostrar contenido de archivo específico
        function showFileContent(fileName) {
            const textOutput = document.getElementById('textOutput');
            const data = extractedData[fileName];
            
            // Actualizar tabs activos
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
                if (tab.textContent === fileName) {
                    tab.classList.add('active');
                }
            });
            
            // Mostrar información del archivo y texto extraído
            const fileInfo = `📄 Archivo: ${data.fileName}
📊 Tamaño: ${data.fileSize}
🕒 Extraído: ${data.extractedAt}
🔧 Método: ${data.method}
📝 Caracteres: ${data.text.length.toLocaleString()}

${'='.repeat(80)}

${data.text}`;
            
            textOutput.textContent = fileInfo;
        }

        // Funciones de utilidad
        function updateProgress() {
            const progress = (currentFileIndex / totalFiles) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        function updateStatusText(text) {
            document.getElementById('statusText').textContent = text;
        }

        function clearResults() {
            extractedData = {};
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('fileInput').value = '';
        }

        function downloadResults() {
            if (Object.keys(extractedData).length === 0) {
                alert('No hay datos para descargar.');
                return;
            }
            
            let content = `EXTRACTOR PDF CON PYTHON - RESULTADOS\n`;
            content += `Generado: ${new Date().toLocaleString('es-ES')}\n`;
            content += `${'='.repeat(80)}\n\n`;
            
            Object.values(extractedData).forEach((data, index) => {
                content += `ARCHIVO ${index + 1}: ${data.fileName}\n`;
                content += `Tamaño: ${data.fileSize}\n`;
                content += `Método: ${data.method}\n`;
                content += `Extraído: ${data.extractedAt}\n`;
                content += `${'='.repeat(50)}\n`;
                content += `${data.text}\n\n`;
                content += `${'='.repeat(80)}\n\n`;
            });
            
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `extraccion_pdf_python_${new Date().toISOString().slice(0, 10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Inicializar cuando se carga la página
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Iniciando aplicación con Python...');
            initializePython();
        });
    </script>
</body>
</html>
