<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extractor Web de Cotizaciones PDF - Versión 1</title>
    <link rel="stylesheet" href="css/estilos.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🎯 Extractor Web de Cotizaciones PDF</h1>
            <p class="subtitle">Versión 1 - 100% Local, Sin Instalación</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- File Upload Section -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📄</div>
                    <h3>Selecciona archivos PDF</h3>
                    <p>Arrastra y suelta archivos PDF aquí o haz clic para seleccionar</p>
                    <input type="file" id="fileInput" accept=".pdf" multiple style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Seleccionar PDFs
                    </button>
                </div>
            </section>

            <!-- Processing Status -->
            <section class="status-section" id="statusSection" style="display: none;">
                <div class="status-info">
                    <div class="spinner"></div>
                    <span id="statusText">Procesando archivos...</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h2>📋 Resultados de Extracción</h2>
                    <div class="results-actions">
                        <button class="btn btn-secondary" onclick="clearResults()">Limpiar</button>
                        <button class="btn btn-primary" onclick="downloadResults()">Descargar Texto</button>
                    </div>
                </div>
                
                <div class="files-tabs" id="filesTabs">
                    <!-- Tabs will be generated dynamically -->
                </div>
                
                <div class="text-output" id="textOutput">
                    <!-- Extracted text will be displayed here -->
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>🛡️ Aplicación 100% local - No requiere conexión a internet</p>
            <p>Desarrollado con HTML5, CSS3, JavaScript y PDF.js</p>
        </footer>
    </div>

    <!-- Error Modal -->
    <div class="modal" id="errorModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚠️ Error</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal()">Cerrar</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- PDF.js desde CDN con fallback local -->
    <script>
        // Intentar cargar PDF.js desde CDN primero, luego fallback local
        function loadPDFJS() {
            const script = document.createElement('script');
            script.onload = function() {
                console.log('✅ PDF.js cargado desde CDN');
                initializePDFJS();
            };
            script.onerror = function() {
                console.log('⚠️ CDN no disponible, intentando carga local...');
                loadLocalPDFJS();
            };
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
            document.head.appendChild(script);
        }

        function loadLocalPDFJS() {
            const script = document.createElement('script');
            script.onload = function() {
                console.log('✅ PDF.js cargado localmente');
                initializePDFJS();
            };
            script.onerror = function() {
                console.log('❌ PDF.js no disponible');
                showPDFJSError();
            };
            script.src = 'libs/pdf.min.js';
            document.head.appendChild(script);
        }

        function initializePDFJS() {
            if (typeof pdfjsLib !== 'undefined') {
                // Configurar worker desde CDN con fallback
                pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

                // Verificar si el worker funciona, si no usar fallback local
                const testWorker = new Worker(pdfjsLib.GlobalWorkerOptions.workerSrc);
                testWorker.onerror = function() {
                    console.log('⚠️ Worker CDN no disponible, usando local...');
                    pdfjsLib.GlobalWorkerOptions.workerSrc = 'libs/pdf.worker.min.js';
                };
                testWorker.terminate();
            }

            // Cargar script principal
            const mainScript = document.createElement('script');
            mainScript.src = 'js/main.js';
            document.head.appendChild(mainScript);
        }

        function showPDFJSError() {
            document.body.innerHTML = `
                <div style="max-width: 800px; margin: 50px auto; padding: 30px; background: #fff; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); font-family: Arial, sans-serif;">
                    <h1 style="color: #e74c3c; text-align: center;">⚠️ PDF.js No Disponible</h1>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>🔧 Solución Rápida:</h3>
                        <ol style="line-height: 1.8;">
                            <li><strong>Ejecuta el script automático:</strong>
                                <ul>
                                    <li>Windows: Doble clic en <code>descargar_pdfjs.bat</code></li>
                                    <li>Mac/Linux: Ejecuta <code>./descargar_pdfjs.sh</code></li>
                                </ul>
                            </li>
                            <li><strong>O descarga manual:</strong>
                                <ul>
                                    <li>Ve a la carpeta <code>libs/</code></li>
                                    <li>Lee <code>INSTRUCCIONES_PDF_JS.md</code></li>
                                </ul>
                            </li>
                            <li><strong>Recarga esta página</strong></li>
                        </ol>
                    </div>
                    <div style="text-align: center; margin-top: 30px;">
                        <button onclick="location.reload()" style="background: #3498db; color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 16px; cursor: pointer;">
                            🔄 Reintentar
                        </button>
                    </div>
                </div>
            `;
        }

        // Iniciar carga cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', loadPDFJS);
    </script>
</body>
</html>
