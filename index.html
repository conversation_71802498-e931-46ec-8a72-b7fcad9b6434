<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extractor Web de Cotizaciones PDF - Versión 1</title>
    <link rel="stylesheet" href="css/estilos.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🎯 Extractor Web de Cotizaciones PDF</h1>
            <p class="subtitle">Versión 1 - 100% Local, Sin Instalación</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- File Upload Section -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📄</div>
                    <h3>Selecciona archivos PDF</h3>
                    <p>Arrastra y suelta archivos PDF aquí o haz clic para seleccionar</p>
                    <input type="file" id="fileInput" accept=".pdf" multiple style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Seleccionar PDFs
                    </button>
                </div>
            </section>

            <!-- Processing Status -->
            <section class="status-section" id="statusSection" style="display: none;">
                <div class="status-info">
                    <div class="spinner"></div>
                    <span id="statusText">Procesando archivos...</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h2>📋 Resultados de Extracción</h2>
                    <div class="results-actions">
                        <button class="btn btn-secondary" onclick="clearResults()">Limpiar</button>
                        <button class="btn btn-primary" onclick="downloadResults()">Descargar Texto</button>
                    </div>
                </div>
                
                <div class="files-tabs" id="filesTabs">
                    <!-- Tabs will be generated dynamically -->
                </div>
                
                <div class="text-output" id="textOutput">
                    <!-- Extracted text will be displayed here -->
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>🛡️ Aplicación 100% local - No requiere conexión a internet</p>
            <p>Desarrollado con HTML5, CSS3, JavaScript y PDF.js</p>
        </footer>
    </div>

    <!-- Error Modal -->
    <div class="modal" id="errorModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚠️ Error</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal()">Cerrar</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="libs/pdf.min.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
