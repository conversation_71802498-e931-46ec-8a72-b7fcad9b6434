<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extractor PDF - Versión Funcionando</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 12px;
            padding: 60px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: rgba(52, 152, 219, 0.05);
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: rgba(52, 152, 219, 0.1);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .upload-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .upload-area h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .upload-area p {
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .upload-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .status-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: none;
        }

        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #ecf0f1;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .results-header h2 {
            color: #2c3e50;
            font-size: 1.8rem;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .text-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .log-section {
            background: rgba(0, 0, 0, 0.8);
            color: #0f0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .upload-area {
                padding: 40px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🎯 Extractor PDF - Versión Funcionando</h1>
            <p class="subtitle">Diagnóstico y Prueba de Funcionalidad</p>
        </header>

        <!-- Upload Section -->
        <section class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📄</div>
                <h3>Selecciona archivos PDF</h3>
                <p>Arrastra y suelta archivos PDF aquí o haz clic para seleccionar</p>
                <input type="file" id="fileInput" accept=".pdf" multiple style="display: none;">
                <button class="upload-btn" id="selectBtn">Seleccionar PDFs</button>
            </div>
        </section>

        <!-- Log Section -->
        <section class="log-section" id="logSection">
            <div id="logContent">🚀 Aplicación iniciada - Esperando archivos...</div>
        </section>

        <!-- Status Section -->
        <section class="status-section" id="statusSection">
            <div class="status-info">
                <div class="spinner"></div>
                <span id="statusText">Procesando archivos...</span>
            </div>
        </section>

        <!-- Results Section -->
        <section class="results-section" id="resultsSection">
            <div class="results-header">
                <h2>📋 Resultados</h2>
                <div>
                    <button class="btn btn-secondary" onclick="clearResults()">Limpiar</button>
                    <button class="btn btn-primary" onclick="downloadResults()">Descargar</button>
                </div>
            </div>
            <div class="text-output" id="textOutput"></div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <p>🛡️ Aplicación 100% local - Versión de diagnóstico</p>
        </footer>
    </div>

    <script>
        // Variables globales
        let extractedData = {};
        let logContent;

        // Función de logging
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `${timestamp} - ${message}`;
            console.log(logMessage);
            
            if (logContent) {
                const div = document.createElement('div');
                div.textContent = logMessage;
                logContent.appendChild(div);
                logContent.scrollTop = logContent.scrollHeight;
            }
        }

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 DOM cargado, iniciando aplicación...');
            initializeApp();
        });

        function initializeApp() {
            // Obtener elementos
            logContent = document.getElementById('logContent');
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const selectBtn = document.getElementById('selectBtn');

            // Verificar elementos
            if (!fileInput) {
                log('❌ ERROR: No se encontró fileInput');
                return;
            }
            if (!uploadArea) {
                log('❌ ERROR: No se encontró uploadArea');
                return;
            }
            if (!selectBtn) {
                log('❌ ERROR: No se encontró selectBtn');
                return;
            }

            log('✅ Todos los elementos encontrados correctamente');

            // Event listeners
            fileInput.addEventListener('change', function(event) {
                log('📁 Evento change disparado en fileInput');
                log(`📊 Archivos seleccionados: ${event.target.files.length}`);
                handleFileSelect(event);
            });

            selectBtn.addEventListener('click', function(event) {
                log('🖱️ Click en botón seleccionar');
                event.preventDefault();
                event.stopPropagation();
                fileInput.click();
            });

            uploadArea.addEventListener('click', function(event) {
                if (event.target !== selectBtn && !selectBtn.contains(event.target)) {
                    log('🖱️ Click en área de upload');
                    fileInput.click();
                }
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', function(event) {
                event.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(event) {
                event.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(event) {
                event.preventDefault();
                uploadArea.classList.remove('dragover');
                log('📂 Drop detectado');
                
                const files = event.dataTransfer.files;
                log(`📊 Archivos dropeados: ${files.length}`);
                
                // Simular evento change
                const changeEvent = new Event('change');
                fileInput.files = files;
                handleFileSelect({ target: { files: files } });
            });

            log('🎯 Aplicación inicializada correctamente');
        }

        // Manejar selección de archivos
        function handleFileSelect(event) {
            log('🔍 handleFileSelect llamado');
            
            const files = Array.from(event.target.files);
            log(`📋 Archivos a procesar: ${files.length}`);

            if (files.length === 0) {
                log('⚠️ No se seleccionaron archivos');
                return;
            }

            processFiles(files);
        }

        // Procesar archivos
        function processFiles(files) {
            log('🚀 Iniciando procesamiento de archivos');
            
            // Mostrar sección de estado
            document.getElementById('statusSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            
            let results = `EXTRACTOR PDF - RESULTADOS\n`;
            results += `Generado: ${new Date().toLocaleString('es-ES')}\n`;
            results += `${'='.repeat(50)}\n\n`;

            files.forEach((file, index) => {
                log(`📄 Procesando archivo ${index + 1}: ${file.name}`);
                
                results += `ARCHIVO ${index + 1}: ${file.name}\n`;
                results += `Tipo: ${file.type || 'Desconocido'}\n`;
                results += `Tamaño: ${formatFileSize(file.size)}\n`;
                results += `Última modificación: ${new Date(file.lastModified).toLocaleString('es-ES')}\n`;
                
                if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
                    results += `Estado: ✅ PDF válido detectado\n`;
                    log(`✅ ${file.name} es un PDF válido`);
                } else {
                    results += `Estado: ⚠️ No es un PDF\n`;
                    log(`⚠️ ${file.name} no parece ser un PDF`);
                }
                
                results += `\n${'='.repeat(50)}\n\n`;
            });

            extractedData = { results: results };

            // Simular procesamiento
            setTimeout(() => {
                log('✅ Procesamiento completado');
                document.getElementById('statusSection').style.display = 'none';
                showResults();
            }, 1500);
        }

        // Mostrar resultados
        function showResults() {
            log('👁️ Mostrando resultados');
            document.getElementById('resultsSection').style.display = 'block';
            document.getElementById('textOutput').textContent = extractedData.results;
        }

        // Limpiar resultados
        function clearResults() {
            log('🧹 Limpiando resultados');
            extractedData = {};
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('fileInput').value = '';
        }

        // Descargar resultados
        function downloadResults() {
            if (!extractedData.results) {
                log('⚠️ No hay datos para descargar');
                return;
            }

            log('💾 Descargando resultados');
            const blob = new Blob([extractedData.results], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `extraccion_pdf_${new Date().toISOString().slice(0, 10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Formatear tamaño de archivo
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        log('🚀 Script cargado completamente');
    </script>
</body>
</html>
