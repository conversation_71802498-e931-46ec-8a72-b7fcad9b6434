<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extractor PDF - Versión Autocontenida</title>
    <style>
        /* Reset y configuración base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* Upload Section */
        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 12px;
            padding: 60px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: rgba(52, 152, 219, 0.05);
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: rgba(52, 152, 219, 0.1);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .upload-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .upload-area h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .upload-area p {
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .upload-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        /* Status Section */
        .status-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: none;
        }

        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #ecf0f1;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Results Section */
        .results-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            flex: 1;
            display: none;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .results-header h2 {
            color: #2c3e50;
            font-size: 1.8rem;
        }

        .results-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* File Tabs */
        .files-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .tab {
            padding: 10px 20px;
            background: #ecf0f1;
            border: none;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab.active {
            background: #3498db;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #bdc3c7;
        }

        /* Text Output */
        .text-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* Footer */
        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            color: #7f8c8d;
        }

        /* Error Modal */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .modal-header h3 {
            color: #e74c3c;
        }

        .close {
            font-size: 1.5rem;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #dee2e6;
            text-align: right;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .upload-area {
                padding: 40px 15px;
            }
            
            .results-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .results-actions {
                justify-content: center;
            }
            
            .files-tabs {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🎯 Extractor PDF Autocontenido</h1>
            <p class="subtitle">Versión 100% Portátil - Sin Dependencias Externas</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- File Upload Section -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📄</div>
                    <h3>Selecciona archivos PDF</h3>
                    <p>Arrastra y suelta archivos PDF aquí o haz clic para seleccionar</p>
                    <input type="file" id="fileInput" accept=".pdf" multiple style="display: none;">
                    <button class="upload-btn" id="selectBtn">
                        Seleccionar PDFs
                    </button>
                </div>
            </section>

            <!-- Processing Status -->
            <section class="status-section" id="statusSection">
                <div class="status-info">
                    <div class="spinner"></div>
                    <span id="statusText">Procesando archivos...</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection">
                <div class="results-header">
                    <h2>📋 Resultados de Extracción</h2>
                    <div class="results-actions">
                        <button class="btn btn-secondary" onclick="clearResults()">Limpiar</button>
                        <button class="btn btn-primary" onclick="downloadResults()">Descargar Texto</button>
                    </div>
                </div>
                
                <div class="files-tabs" id="filesTabs">
                    <!-- Tabs will be generated dynamically -->
                </div>
                
                <div class="text-output" id="textOutput">
                    <!-- Extracted text will be displayed here -->
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>🛡️ Aplicación 100% local - No requiere conexión a internet</p>
            <p>⚠️ Versión simplificada - Para PDFs complejos use la versión completa con PDF.js</p>
        </footer>
    </div>

    <!-- Error Modal -->
    <div class="modal" id="errorModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚠️ Error</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal()">Cerrar</button>
            </div>
        </div>
    </div>

    <script>
        // Configuración global
        let extractedData = {};
        let currentFileIndex = 0;
        let totalFiles = 0;

        // Inicialización cuando se carga la página
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            console.log('🔧 Iniciando aplicación...');

            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const selectBtn = document.getElementById('selectBtn');

            if (!fileInput) {
                console.error('❌ No se encontró el elemento fileInput');
                return;
            }

            if (!uploadArea) {
                console.error('❌ No se encontró el elemento uploadArea');
                return;
            }

            if (!selectBtn) {
                console.error('❌ No se encontró el elemento selectBtn');
                return;
            }

            console.log('✅ Elementos encontrados:', { fileInput, uploadArea, selectBtn });

            // Event listeners para el input de archivos
            fileInput.addEventListener('change', function(event) {
                console.log('📁 Archivo seleccionado:', event.target.files);
                handleFileSelect(event);
            });

            // Event listeners para el botón de seleccionar
            selectBtn.addEventListener('click', function(event) {
                console.log('🖱️ Click en botón seleccionar');
                event.preventDefault();
                event.stopPropagation();
                fileInput.click();
            });

            // Event listeners para drag and drop
            uploadArea.addEventListener('dragover', function(event) {
                event.preventDefault();
                event.stopPropagation();
                uploadArea.classList.add('dragover');
                console.log('🔄 Drag over detectado');
            });

            uploadArea.addEventListener('dragleave', function(event) {
                event.preventDefault();
                event.stopPropagation();
                uploadArea.classList.remove('dragover');
                console.log('🔄 Drag leave detectado');
            });

            uploadArea.addEventListener('drop', function(event) {
                event.preventDefault();
                event.stopPropagation();
                uploadArea.classList.remove('dragover');
                console.log('📂 Drop detectado');

                const files = event.dataTransfer.files;
                console.log(`📊 Archivos dropeados: ${files.length}`);

                // Filtrar solo PDFs
                const pdfFiles = Array.from(files).filter(file =>
                    file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
                );

                if (pdfFiles.length > 0) {
                    console.log(`✅ Procesando ${pdfFiles.length} archivos PDF`);
                    processFiles(pdfFiles);
                } else {
                    console.log('⚠️ No se encontraron archivos PDF válidos');
                    showError('Por favor, selecciona solo archivos PDF válidos.');
                }
            });

            // Agregar click al área de upload (excluyendo el botón)
            uploadArea.addEventListener('click', function(event) {
                // Solo activar si no se hizo click en el botón
                if (event.target !== selectBtn && !selectBtn.contains(event.target)) {
                    console.log('🖱️ Click en área de upload');
                    fileInput.click();
                }
            });

            console.log('🎯 Extractor PDF autocontenido inicializado');

            // Mostrar advertencia sobre limitaciones
            showInfo('Esta versión autocontenida tiene limitaciones. Para PDFs complejos, use la versión completa con PDF.js.');
        }

        // Manejo de selección de archivos
        function handleFileSelect(event) {
            console.log('🔍 handleFileSelect llamado:', event);
            console.log('📂 event.target:', event.target);
            console.log('📁 event.target.files:', event.target.files);

            const files = Array.from(event.target.files);
            console.log('📋 Archivos procesados:', files);
            console.log('🔢 Cantidad de archivos:', files.length);

            if (files.length > 0) {
                console.log('✅ Procesando archivos...');
                processFiles(files);
            } else {
                console.log('⚠️ No se seleccionaron archivos');
            }
        }



        // Procesar archivos PDF (versión simplificada)
        async function processFiles(files) {
            console.log('🚀 Iniciando processFiles con:', files);

            totalFiles = files.length;
            currentFileIndex = 0;
            extractedData = {};

            console.log('📊 Configuración:', { totalFiles, currentFileIndex });

            // Mostrar sección de estado
            console.log('👁️ Mostrando sección de estado...');
            showStatusSection();
            hideResultsSection();
            
            try {
                for (let i = 0; i < files.length; i++) {
                    currentFileIndex = i;
                    updateProgress();
                    updateStatusText(`Procesando ${files[i].name}...`);
                    
                    // Versión simplificada: solo mostrar información del archivo
                    const fileInfo = await getFileInfo(files[i]);
                    extractedData[files[i].name] = {
                        text: fileInfo,
                        fileName: files[i].name,
                        fileSize: formatFileSize(files[i].size),
                        extractedAt: new Date().toLocaleString('es-ES')
                    };
                }
                
                // Completar procesamiento
                currentFileIndex = totalFiles;
                updateProgress();
                updateStatusText('¡Procesamiento completado!');
                
                setTimeout(() => {
                    hideStatusSection();
                    showResults();
                }, 1000);
                
            } catch (error) {
                console.error('Error procesando archivos:', error);
                hideStatusSection();
                showError(`Error procesando archivos: ${error.message}`);
            }
        }

        // Obtener información básica del archivo (sin extraer texto real)
        async function getFileInfo(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const arrayBuffer = e.target.result;
                    const uint8Array = new Uint8Array(arrayBuffer);
                    
                    // Verificar si es un PDF válido
                    const pdfHeader = String.fromCharCode.apply(null, uint8Array.slice(0, 5));
                    
                    if (pdfHeader === '%PDF-') {
                        const info = `📄 ARCHIVO PDF DETECTADO

🔍 INFORMACIÓN BÁSICA:
- Nombre: ${file.name}
- Tamaño: ${formatFileSize(file.size)}
- Tipo: ${file.type}
- Última modificación: ${new Date(file.lastModified).toLocaleString('es-ES')}

⚠️ LIMITACIÓN DE VERSIÓN AUTOCONTENIDA:
Esta versión simplificada no puede extraer el texto completo del PDF.

🚀 PARA EXTRACCIÓN COMPLETA:
1. Use la versión completa (index.html)
2. Descargue PDF.js siguiendo las instrucciones
3. O use el script automático de descarga

📋 CONTENIDO DETECTADO:
- Archivo PDF válido
- Versión PDF: ${pdfHeader}
- Procesamiento básico completado

💡 PRÓXIMOS PASOS:
Para extraer texto real de cotizaciones, use la versión completa con PDF.js instalado.`;
                        
                        resolve(info);
                    } else {
                        resolve('❌ Error: El archivo no parece ser un PDF válido.');
                    }
                };
                reader.readAsArrayBuffer(file);
            });
        }

        // Mostrar resultados
        function showResults() {
            const resultsSection = document.getElementById('resultsSection');
            const filesTabs = document.getElementById('filesTabs');
            const textOutput = document.getElementById('textOutput');
            
            // Limpiar tabs existentes
            filesTabs.innerHTML = '';
            
            // Crear tabs para cada archivo
            const fileNames = Object.keys(extractedData);
            fileNames.forEach((fileName, index) => {
                const tab = document.createElement('button');
                tab.className = `tab ${index === 0 ? 'active' : ''}`;
                tab.textContent = fileName;
                tab.onclick = () => showFileContent(fileName);
                filesTabs.appendChild(tab);
            });
            
            // Mostrar contenido del primer archivo
            if (fileNames.length > 0) {
                showFileContent(fileNames[0]);
            }
            
            resultsSection.style.display = 'block';
        }

        // Mostrar contenido de un archivo específico
        function showFileContent(fileName) {
            const textOutput = document.getElementById('textOutput');
            const data = extractedData[fileName];
            
            // Actualizar tabs activos
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
                if (tab.textContent === fileName) {
                    tab.classList.add('active');
                }
            });
            
            textOutput.textContent = data.text;
        }

        // Funciones de utilidad para la interfaz
        function showStatusSection() {
            console.log('👁️ Mostrando sección de estado');
            const statusSection = document.getElementById('statusSection');
            if (statusSection) {
                statusSection.style.display = 'block';
                console.log('✅ Sección de estado mostrada');
            } else {
                console.error('❌ No se encontró statusSection');
            }
        }

        function hideStatusSection() {
            console.log('🙈 Ocultando sección de estado');
            const statusSection = document.getElementById('statusSection');
            if (statusSection) {
                statusSection.style.display = 'none';
            }
        }

        function hideResultsSection() {
            console.log('🙈 Ocultando sección de resultados');
            const resultsSection = document.getElementById('resultsSection');
            if (resultsSection) {
                resultsSection.style.display = 'none';
            }
        }

        function updateProgress() {
            const progress = (currentFileIndex / totalFiles) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        function updateStatusText(text) {
            document.getElementById('statusText').textContent = text;
        }

        // Limpiar resultados
        function clearResults() {
            extractedData = {};
            hideResultsSection();
            document.getElementById('fileInput').value = '';
        }

        // Descargar resultados como archivo de texto
        function downloadResults() {
            if (Object.keys(extractedData).length === 0) {
                showError('No hay datos para descargar.');
                return;
            }
            
            let content = `EXTRACTOR PDF AUTOCONTENIDO - RESULTADOS\n`;
            content += `Generado: ${new Date().toLocaleString('es-ES')}\n`;
            content += `${'='.repeat(80)}\n\n`;
            
            Object.values(extractedData).forEach((data, index) => {
                content += `ARCHIVO ${index + 1}: ${data.fileName}\n`;
                content += `Tamaño: ${data.fileSize}\n`;
                content += `Procesado: ${data.extractedAt}\n`;
                content += `${'='.repeat(50)}\n`;
                content += `${data.text}\n\n`;
                content += `${'='.repeat(80)}\n\n`;
            });
            
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `info_pdf_${new Date().toISOString().slice(0, 10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Mostrar errores
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorModal').style.display = 'flex';
        }

        // Mostrar información
        function showInfo(message) {
            console.log('ℹ️ Info:', message);
        }

        // Cerrar modal
        function closeModal() {
            document.getElementById('errorModal').style.display = 'none';
        }

        // Formatear tamaño de archivo
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        console.log('🚀 Extractor PDF autocontenido cargado');

        // Test de funcionalidad después de cargar
        setTimeout(function() {
            console.log('🧪 Ejecutando test de funcionalidad...');

            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const selectBtn = document.getElementById('selectBtn');

            console.log('🔍 Test de elementos:');
            console.log('- fileInput:', fileInput ? '✅ Encontrado' : '❌ No encontrado');
            console.log('- uploadArea:', uploadArea ? '✅ Encontrado' : '❌ No encontrado');
            console.log('- selectBtn:', selectBtn ? '✅ Encontrado' : '❌ No encontrado');

            if (fileInput) {
                console.log('📊 Propiedades de fileInput:');
                console.log('- type:', fileInput.type);
                console.log('- accept:', fileInput.accept);
                console.log('- multiple:', fileInput.multiple);
                console.log('- style.display:', fileInput.style.display);
            }

            console.log('🧪 Test completado');
        }, 1000);
    </script>
</body>
</html>
