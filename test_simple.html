<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple PDF</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            background: #f8f9fa;
            border-color: #0056b3;
        }
        
        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #1976d2;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .results {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Simple - Carga de PDF</h1>
        <p>Versión de prueba para diagnosticar problemas de carga de archivos</p>
        
        <div class="upload-area" id="uploadArea">
            <h3>📄 Selecciona o arrastra archivos PDF</h3>
            <p>Haz clic aquí o arrastra archivos PDF</p>
            <input type="file" id="fileInput" accept=".pdf" multiple style="display: none;">
            <button class="btn" id="selectBtn">Seleccionar Archivos</button>
        </div>
        
        <div class="log" id="log">
            <div>🚀 Aplicación iniciada - Esperando archivos...</div>
        </div>
        
        <div class="results" id="results">
            <h3>📋 Archivos Detectados:</h3>
            <div id="fileList"></div>
        </div>
    </div>

    <script>
        // Variables globales
        let logElement;
        let fileInput;
        let uploadArea;
        let selectBtn;
        let results;
        let fileList;

        // Función de logging
        function log(message) {
            console.log(message);
            if (logElement) {
                const div = document.createElement('div');
                div.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
                logElement.appendChild(div);
                logElement.scrollTop = logElement.scrollHeight;
            }
        }

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 DOM cargado, iniciando aplicación...');
            
            // Obtener elementos
            logElement = document.getElementById('log');
            fileInput = document.getElementById('fileInput');
            uploadArea = document.getElementById('uploadArea');
            selectBtn = document.getElementById('selectBtn');
            results = document.getElementById('results');
            fileList = document.getElementById('fileList');
            
            // Verificar elementos
            if (!fileInput) {
                log('❌ ERROR: No se encontró fileInput');
                return;
            }
            if (!uploadArea) {
                log('❌ ERROR: No se encontró uploadArea');
                return;
            }
            if (!selectBtn) {
                log('❌ ERROR: No se encontró selectBtn');
                return;
            }
            
            log('✅ Todos los elementos encontrados');
            
            // Event listeners
            fileInput.addEventListener('change', function(event) {
                log('📁 Evento change disparado en fileInput');
                log(`📊 Archivos seleccionados: ${event.target.files.length}`);
                handleFiles(event.target.files);
            });
            
            selectBtn.addEventListener('click', function(event) {
                log('🖱️ Click en botón seleccionar');
                event.preventDefault();
                fileInput.click();
            });
            
            uploadArea.addEventListener('click', function(event) {
                if (event.target !== selectBtn) {
                    log('🖱️ Click en área de upload');
                    fileInput.click();
                }
            });
            
            // Drag and drop
            uploadArea.addEventListener('dragover', function(event) {
                event.preventDefault();
                uploadArea.classList.add('dragover');
                log('🔄 Drag over detectado');
            });
            
            uploadArea.addEventListener('dragleave', function(event) {
                event.preventDefault();
                uploadArea.classList.remove('dragover');
                log('🔄 Drag leave detectado');
            });
            
            uploadArea.addEventListener('drop', function(event) {
                event.preventDefault();
                uploadArea.classList.remove('dragover');
                log('📂 Drop detectado');
                
                const files = event.dataTransfer.files;
                log(`📊 Archivos dropeados: ${files.length}`);
                handleFiles(files);
            });
            
            log('🎯 Aplicación inicializada correctamente');
        });

        // Manejar archivos
        function handleFiles(files) {
            log(`🚀 Procesando ${files.length} archivo(s)`);
            
            if (files.length === 0) {
                log('⚠️ No se seleccionaron archivos');
                return;
            }
            
            // Mostrar resultados
            results.style.display = 'block';
            fileList.innerHTML = '';
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                log(`📄 Archivo ${i + 1}: ${file.name} (${file.type}) - ${formatFileSize(file.size)}`);
                
                const fileDiv = document.createElement('div');
                fileDiv.style.cssText = 'margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #ddd;';
                fileDiv.innerHTML = `
                    <strong>📄 ${file.name}</strong><br>
                    <small>Tipo: ${file.type || 'Desconocido'}</small><br>
                    <small>Tamaño: ${formatFileSize(file.size)}</small><br>
                    <small>Última modificación: ${new Date(file.lastModified).toLocaleString()}</small>
                `;
                fileList.appendChild(fileDiv);
                
                // Verificar si es PDF
                if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
                    log(`✅ ${file.name} es un PDF válido`);
                    readPDFBasicInfo(file);
                } else {
                    log(`⚠️ ${file.name} no parece ser un PDF`);
                }
            }
        }

        // Leer información básica del PDF
        function readPDFBasicInfo(file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const arrayBuffer = e.target.result;
                const uint8Array = new Uint8Array(arrayBuffer);
                
                // Verificar header PDF
                const header = String.fromCharCode.apply(null, uint8Array.slice(0, 8));
                log(`📋 Header de ${file.name}: ${header}`);
                
                if (header.startsWith('%PDF-')) {
                    log(`✅ ${file.name} confirmado como PDF válido`);
                } else {
                    log(`❌ ${file.name} no tiene header PDF válido`);
                }
            };
            
            reader.onerror = function() {
                log(`❌ Error leyendo ${file.name}`);
            };
            
            reader.readAsArrayBuffer(file);
        }

        // Formatear tamaño de archivo
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Test de funcionalidad
        function testFileInput() {
            log('🧪 Ejecutando test de fileInput...');
            if (fileInput) {
                log('✅ fileInput existe');
                log(`📊 fileInput.type: ${fileInput.type}`);
                log(`📊 fileInput.accept: ${fileInput.accept}`);
                log(`📊 fileInput.multiple: ${fileInput.multiple}`);
            } else {
                log('❌ fileInput no existe');
            }
        }

        // Ejecutar test después de 2 segundos
        setTimeout(testFileInput, 2000);
    </script>
</body>
</html>
