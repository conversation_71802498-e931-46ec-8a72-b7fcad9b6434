# 🎯 Extractor Web de Cotizaciones PDF - Versión 1

## 📝 Descripción
Aplicación web local y portátil para extraer texto de archivos PDF de cotizaciones. **100% local, sin instalación ni conexión a internet requerida**.

## ✨ Características

### ✅ Implementado en Versión 1
- 📄 Carga de múltiples archivos PDF
- 🔍 Extracción completa de texto con PDF.js
- 👀 Visualización de texto extraído en pantalla
- 💾 Descarga de resultados en formato texto
- 🎨 Interfaz moderna y responsiva
- 🛡️ Funcionamiento 100% local y seguro
- 📱 Compatible con dispositivos móviles

### 🔜 Planificado para Versión 2
- 🎯 Extracción automática de campos específicos:
  - Nombre del cliente
  - Fecha de cotización
  - Código de orden/número de cotización
  - Detalle de artículos cotizados
- 📊 Exportación a Excel/JSON
- 🔍 Búsqueda y filtrado de contenido

## 🚀 Instalación y Uso

### Paso 1: Preparar PDF.js
1. Ve a la carpeta `libs/`
2. <PERSON> las instrucciones en `INSTRUCCIONES_PDF_JS.md`
3. Descarga los archivos requeridos de PDF.js

### Paso 2: Ejecutar la aplicación
1. Haz doble clic en `index.html`
2. La aplicación se abrirá en tu navegador predeterminado
3. ¡Listo para usar!

### Paso 3: Extraer texto de PDFs
1. Arrastra archivos PDF a la zona de carga o haz clic en "Seleccionar PDFs"
2. Espera a que se complete el procesamiento
3. Revisa el texto extraído en las pestañas
4. Descarga los resultados si es necesario

## 📦 Estructura del Proyecto

```
pdf-extractor-v1/
├── index.html                    # Página principal
├── css/
│   └── estilos.css              # Estilos de la aplicación
├── js/
│   └── main.js                  # Lógica principal
├── libs/
│   ├── INSTRUCCIONES_PDF_JS.md  # Guía para descargar PDF.js
│   ├── pdf.min.js              # (Descargar) Biblioteca PDF.js
│   └── pdf.worker.min.js       # (Descargar) Worker de PDF.js
└── README.md                    # Este archivo
```

## 💻 Tecnologías Utilizadas

- **HTML5** - Estructura de la aplicación
- **CSS3** - Estilos modernos con gradientes y efectos
- **JavaScript ES6+** - Lógica de la aplicación
- **PDF.js** - Biblioteca para procesamiento de PDFs
- **File API** - Manejo de archivos locales
- **Drag & Drop API** - Interfaz intuitiva de carga

## 🛡️ Seguridad y Privacidad

- ✅ **100% Local**: No se envían datos a servidores externos
- ✅ **Sin instalación**: Funciona directamente desde el navegador
- ✅ **Sin conexión**: No requiere internet para funcionar
- ✅ **Privacidad total**: Los PDFs nunca salen de tu computadora
- ✅ **Compatible con políticas IT**: Cumple con restricciones corporativas

## 🔧 Requisitos del Sistema

- **Navegador moderno** (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)
- **JavaScript habilitado**
- **Archivos PDF.js descargados** (ver instrucciones en `libs/`)

## 📱 Compatibilidad

- ✅ Windows 10/11
- ✅ macOS 10.14+
- ✅ Linux (distribuciones modernas)
- ✅ Dispositivos móviles (iOS Safari, Android Chrome)

## 🐛 Solución de Problemas

### Error: "PDF.js no está cargado"
- Verifica que hayas descargado `pdf.min.js` en la carpeta `libs/`

### Error: "Worker no encontrado"
- Verifica que hayas descargado `pdf.worker.min.js` en la carpeta `libs/`

### Los PDFs no se procesan
- Verifica que los archivos sean PDFs válidos
- Abre la consola del navegador (F12) para ver errores específicos

### La aplicación no se ve correctamente
- Asegúrate de que `css/estilos.css` esté presente
- Verifica que tu navegador sea compatible

## 📞 Soporte

Si encuentras problemas:

1. Revisa la consola del navegador (F12) para errores
2. Verifica que todos los archivos estén en su lugar
3. Asegúrate de que los PDFs no estén corruptos
4. Prueba con un navegador diferente

## 🔄 Historial de Versiones

### v1.0.0 (Actual)
- ✅ Extracción básica de texto de PDFs
- ✅ Interfaz web moderna
- ✅ Soporte para múltiples archivos
- ✅ Descarga de resultados

### v2.0.0 (Planificado)
- 🔜 Extracción estructurada de campos
- 🔜 Exportación a Excel/JSON
- 🔜 Búsqueda y filtrado

## 📄 Licencia

Este proyecto es de uso libre para fines educativos y comerciales.

---

**Desarrollado con ❤️ para facilitar la extracción de datos de cotizaciones PDF**
