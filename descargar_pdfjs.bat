@echo off
echo 🎯 Descargando PDF.js para el Extractor de Cotizaciones PDF...
echo.

REM Crear directorio libs si no existe
if not exist "libs" mkdir libs

echo 📥 Descargando pdf.min.js...
powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js' -OutFile 'libs/pdf.min.js'"

echo 📥 Descargando pdf.worker.min.js...
powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js' -OutFile 'libs/pdf.worker.min.js'"

echo.
echo ✅ Descarga completada!
echo.
echo 🚀 Ahora puedes abrir index.html para usar la aplicación.
echo.
pause
