# 📦 Instrucciones para PDF.js

## ¿Qué es PDF.js?
PDF.js es una biblioteca de JavaScript desarrollada por Mozilla que permite leer y renderizar archivos PDF directamente en el navegador, sin necesidad de plugins externos.

## 📥 Descarga Requerida

Para que la aplicación funcione correctamente, necesitas descargar los siguientes archivos de PDF.js:

### Archivos necesarios:
1. **pdf.min.js** - Biblioteca principal
2. **pdf.worker.min.js** - Worker para procesamiento en segundo plano

### 🔗 Enlaces de descarga:

**Opción 1: Descarga directa desde CDN**
```
https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js
https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js
```

**Opción 2: Desde el repositorio oficial**
- Visita: https://github.com/mozilla/pdf.js/releases
- Descarga la versión más reciente
- Extrae los archivos `pdf.min.js` y `pdf.worker.min.js` de la carpeta `build/`

## 📂 Ubicación de archivos

Coloca los archivos descargados en esta carpeta (`libs/`) con los siguientes nombres:

```
libs/
├── pdf.min.js          ← Archivo principal
├── pdf.worker.min.js   ← Worker
└── INSTRUCCIONES_PDF_JS.md ← Este archivo
```

## ✅ Verificación

Una vez descargados los archivos:

1. Abre `index.html` en tu navegador
2. Si ves la interfaz sin errores en la consola, ¡está funcionando!
3. Prueba cargando un archivo PDF

## ⚠️ Notas importantes

- **Sin estos archivos, la aplicación NO funcionará**
- Los archivos deben tener exactamente los nombres especificados
- Asegúrate de descargar archivos de la misma versión de PDF.js
- La aplicación está configurada para funcionar con PDF.js v3.11.174 o superior

## 🔧 Solución de problemas

**Error: "PDF.js no está cargado"**
- Verifica que `pdf.min.js` esté en la carpeta `libs/`
- Verifica que el nombre del archivo sea exacto

**Error: "Worker no encontrado"**
- Verifica que `pdf.worker.min.js` esté en la carpeta `libs/`
- Verifica que el nombre del archivo sea exacto

**PDFs no se procesan**
- Abre la consola del navegador (F12) para ver errores específicos
- Verifica que los archivos PDF no estén corruptos
