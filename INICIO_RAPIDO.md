# 🚀 Inicio Rápido - Extractor PDF

## 🎯 Dos Opciones Disponibles

### ⚡ OPCIÓN 1: Máxima Portabilidad (Recomendada para IT estricto)
```
Haz doble clic en: index_autocontenido.html
```
✅ **Funciona inmediatamente** - Sin configuración
⚠️ **Limitación**: Solo información básica de PDFs

### 📄 OPCIÓN 2: Funcionalidad Completa
#### Paso 1: Configurar PDF.js (una sola vez)
**Automático - Windows:**
```
Haz doble clic en: descargar_pdfjs.bat
```

**Automático - Mac/Linux:**
```bash
./descargar_pdfjs.sh
```

**Manual:**
1. Ve a la carpeta `libs/`
2. Lee `INSTRUCCIONES_PDF_JS.md`
3. Descarga los archivos manualmente

#### Paso 2: Abrir la aplicación
```
Haz doble clic en: index.html
```

### 📋 Uso General (ambas versiones)
1. Arrastra PDFs a la zona de carga
2. Espera el procesamiento
3. Revisa los resultados
4. Descarga si necesitas

## 🎯 Ejemplo de uso

### Archivos de cotización típicos:
- `cotizacion_cliente_001.pdf`
- `presupuesto_enero_2025.pdf`
- `orden_repuestos_250003367.pdf`

### Datos que se extraerán:
- ✅ Todo el texto del PDF
- ✅ Información por páginas
- ✅ Metadatos del archivo
- 🔜 Campos estructurados (v2.0)

## ⚠️ Verificación rápida

### ✅ Todo funciona si:
- La página se carga sin errores
- Puedes arrastrar archivos PDF
- Se muestra el progreso de extracción
- Aparece el texto extraído

### ❌ Hay problemas si:
- Aparece "PDF.js no está cargado"
- Los archivos no se procesan
- La página se ve mal

## 🔧 Solución rápida de problemas

1. **Abre la consola del navegador** (F12)
2. **Busca errores en rojo**
3. **Verifica que existan estos archivos:**
   ```
   ✅ index.html
   ✅ css/estilos.css
   ✅ js/main.js
   ✅ libs/pdf.min.js
   ✅ libs/pdf.worker.min.js
   ```

## 📞 ¿Necesitas ayuda?

1. Lee el `README.md` completo
2. Revisa `libs/INSTRUCCIONES_PDF_JS.md`
3. Verifica la consola del navegador
4. Prueba con un PDF diferente

---

**¡Listo para extraer texto de tus PDFs! 🎉**
